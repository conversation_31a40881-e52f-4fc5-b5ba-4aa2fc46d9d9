package com.taobao.wireless.orange.external.hsf;

import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.alibaba.ihr.amdplatform.service.api.AmdpDataQueryService;
import org.springframework.context.annotation.Configuration;

@Configuration
public class HsfConfig {
    @HSFConsumer(serviceVersion = "${amdp.hsf.version}", serviceGroup = "${amdp.hsf.group}", clientTimeout = 5000)
    private AmdpDataQueryService amdpDataQueryService;
}

