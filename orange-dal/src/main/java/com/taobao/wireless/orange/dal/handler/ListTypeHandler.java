package com.taobao.wireless.orange.dal.handler;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.springframework.util.StringUtils;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes({List.class})
public class ListTypeHandler extends BaseTypeHandler<List<String>> {

    private static final String DELIM = ",";

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, List<String> strings, JdbcType jdbcType) throws SQLException {
        if (CollectionUtils.isNotEmpty(strings)) {
            String value = StringUtils.collectionToDelimitedString(strings, DELIM);
            preparedStatement.setString(i, value);
        }
    }

    @Override
    public List<String> getNullableResult(ResultSet resultSet, String s) throws SQLException {
        String value = resultSet.getString(s);
        if (org.apache.dubbo.common.utils.StringUtils.isNotBlank(value)) {
            return Arrays.asList(StringUtils.tokenizeToStringArray(value, DELIM));
        }
        return new ArrayList<>();
    }

    @Override
    public List<String> getNullableResult(ResultSet resultSet, int i) throws SQLException {
        String value = resultSet.getString(i);
        if (org.apache.dubbo.common.utils.StringUtils.isNotBlank(value)) {
            return Arrays.asList(StringUtils.tokenizeToStringArray(value, DELIM));
        }
        return new ArrayList<>();
    }

    @Override
    public List<String> getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        String value = callableStatement.getString(i);
        if (org.apache.dubbo.common.utils.StringUtils.isNotBlank(value)) {
            return Arrays.asList(StringUtils.tokenizeToStringArray(value, DELIM));
        }
        return new ArrayList<>();
    }
}
