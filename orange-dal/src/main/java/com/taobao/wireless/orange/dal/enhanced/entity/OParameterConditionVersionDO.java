package com.taobao.wireless.orange.dal.enhanced.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.taobao.wireless.orange.common.constant.enums.ParameterConditionChangeType;
import com.taobao.wireless.orange.common.constant.enums.VersionStatus;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 参数条件版本表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@TableName("o_parameter_condition_version")
public class OParameterConditionVersionDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 参数ID
     */
    @TableField("parameter_id")
    private String parameterId;

    /**
     * 条件ID
     */
    @TableField("condition_id")
    private String conditionId;

    /**
     * 发布版本号
     */
    @TableField("release_version")
    private String releaseVersion;

    /**
     * 应用KEY
     */
    @TableField("app_key")
    private String appKey;

    /**
     * 命名空间ID
     */
    @TableField("namespace_id")
    private String namespaceId;

    /**
     * 修改前发布版本号
     */
    @TableField("previous_release_version")
    private String previousReleaseVersion;

    /**
     * 参数条件值
     */
    @TableField("value")
    private String value;

    /**
     * 变更类型
     */
    @TableField("change_type")
    private ParameterConditionChangeType changeType;

    /**
     * 状态
     */
    @TableField("status")
    private VersionStatus status;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 创建者
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 修改者
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;
}
