package com.taobao.wireless.orange.web.aspect;

import com.alibaba.buc.sso.client.util.SimpleUserUtil;
import com.alibaba.buc.sso.client.vo.BucSSOUser;
import com.taobao.wireless.orange.common.thread.OThreadContext;
import com.taobao.wireless.orange.common.thread.OThreadContextHolder;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * 用户上下文切面
 * 从请求中获取用户信息，并注入到线程上下文中
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Order(0)
@Slf4j
public class UserContextAspect {

    @Pointcut("execution(* com.taobao.wireless.orange.web.controller.*.*(..))")
    public void controllerPointcut() {
    }

    @Around("controllerPointcut()")
    public Object setUserContext(ProceedingJoinPoint joinPoint) throws Throwable {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return joinPoint.proceed();
        }

        HttpServletRequest request = attributes.getRequest();

        try {
            // 从请求中获取用户信息
            BucSSOUser user = SimpleUserUtil.getBucSSOUser(request);
            if (user != null) {
                var threadContext = new OThreadContext();
                String workerId = StringUtils.leftPad(user.getEmpId(), 6, '0');
                threadContext.setWorkerId(workerId);
                threadContext.setWorkerName(user.getNickNameCn());
                OThreadContextHolder.set(threadContext);
            }

            return joinPoint.proceed();
        } catch (Exception e) {
            log.error("Failed to set user context", e);
            return joinPoint.proceed();
        } finally {
            OThreadContextHolder.clear();
        }
    }
}