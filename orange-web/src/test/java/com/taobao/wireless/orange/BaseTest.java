package com.taobao.wireless.orange;

import com.taobao.pandora.boot.test.junit4.DelegateTo;
import com.taobao.pandora.boot.test.junit4.PandoraBootRunner;
import com.taobao.wireless.orange.common.thread.OThreadContext;
import com.taobao.wireless.orange.common.thread.OThreadContextHolder;
import com.taobao.wireless.orange.web.Application;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Date;
import java.util.UUID;

/**
 * 测试基类 - 提供统一的测试注解和通用功能
 *
 * <AUTHOR>
@RunWith(PandoraBootRunner.class)
@DelegateTo(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles(profiles = "testing")
public abstract class BaseTest {

    /**
     * 默认测试用户ID
     */
    protected static final String DEFAULT_WORKER_ID = "149016";

    /**
     * 默认测试用户名
     */
    protected static final String DEFAULT_WORKER_NAME = "测试用户";

    /**
     * 默认测试应用Key
     */
    protected static final String DEFAULT_APP_KEY = "test-app-key";

    /**
     * 测试前置设置
     * 自动设置Orange框架的线程上下文信息
     */
    @Before
    public void baseSetUp() throws Exception {
        setupThreadContext();
        customSetUp();
    }

    /**
     * 设置线程上下文
     * 为Orange框架提供必要的用户信息
     */
    protected void setupThreadContext() {
        setupThreadContext(DEFAULT_WORKER_ID, DEFAULT_WORKER_NAME);
    }

    /**
     * 设置自定义线程上下文
     *
     * @param workerId   工作者ID
     * @param workerName 工作者名称
     */
    protected void setupThreadContext(String workerId, String workerName) {
        OThreadContext threadContext = new OThreadContext();
        threadContext.setWorkerId(workerId);
        threadContext.setWorkerName(workerName);
        OThreadContextHolder.set(threadContext);
    }

    /**
     * 自定义设置方法，子类可以重写此方法进行额外的初始化
     */
    protected void customSetUp() throws Exception {
        // 子类可以重写此方法
    }

    // ==================== 通用测试工具方法 ====================

    /**
     * 生成测试用的唯一ID
     *
     * @return 唯一ID字符串
     */
    protected String generateTestId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成测试用的短ID（8位）
     *
     * @return 8位唯一ID
     */
    protected String generateShortTestId() {
        return UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 生成测试用的版本号
     *
     * @param prefix 版本前缀
     * @return 版本号字符串
     */
    protected String generateTestVersion(String prefix) {
        return prefix + "-" + System.currentTimeMillis();
    }

    /**
     * 生成测试用的版本号（默认前缀为test）
     *
     * @return 版本号字符串
     */
    protected String generateTestVersion() {
        return generateTestVersion("test");
    }

    /**
     * 生成测试用的命名空间名称
     *
     * @return 命名空间名称
     */
    protected String generateTestNamespaceName() {
        return "测试命名空间-" + generateShortTestId();
    }

    /**
     * 生成测试用的参数名称
     *
     * @return 参数名称
     */
    protected String generateTestParameterName() {
        return "test_param_" + generateShortTestId();
    }

    /**
     * 获取当前时间
     *
     * @return 当前时间
     */
    protected Date getCurrentTime() {
        return new Date();
    }

    /**
     * 等待指定毫秒数
     *
     * @param milliseconds 等待时间（毫秒）
     */
    protected void waitFor(long milliseconds) {
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("等待被中断", e);
        }
    }

    /**
     * 断言结果成功
     *
     * @param result  结果对象（需要有isSuccess方法）
     * @param message 失败时的错误信息
     */
    protected void assertSuccess(Object result, String message) {
        try {
            Boolean success = (Boolean) result.getClass().getMethod("isSuccess").invoke(result);
            if (!success) {
                throw new AssertionError(message + " - 操作失败");
            }
        } catch (Exception e) {
            throw new AssertionError(message + " - 无法检查结果状态: " + e.getMessage());
        }
    }

    /**
     * 断言结果成功（使用默认错误信息）
     *
     * @param result 结果对象
     */
    protected void assertSuccess(Object result) {
        assertSuccess(result, "操作应该成功");
    }

    // ==================== 测试数据清理方法 ====================

    /**
     * 清理测试数据的方法，子类可以重写
     * 在某些测试场景下可能需要手动清理数据
     */
    protected void cleanupTestData() {
        // 子类可以重写此方法进行数据清理
    }

    // ==================== 日志输出方法 ====================

    /**
     * 输出测试信息
     *
     * @param message 信息内容
     */
    protected void logTestInfo(String message) {
        System.out.println("[TEST INFO] " + message);
    }

    /**
     * 输出测试警告
     *
     * @param message 警告内容
     */
    protected void logTestWarning(String message) {
        System.out.println("[TEST WARNING] " + message);
    }

    /**
     * 输出测试错误
     *
     * @param message 错误内容
     */
    protected void logTestError(String message) {
        System.err.println("[TEST ERROR] " + message);
    }
}
