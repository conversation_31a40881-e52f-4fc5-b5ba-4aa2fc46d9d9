package com.taobao.wireless.orange.common.thread;

public class OThreadContextHolder {

    private static final ThreadLocal<OThreadContext> threadLocal = new ThreadLocal<>();

    public static OThreadContext get() {
        return threadLocal.get();
    }

    public static void clear() {
        threadLocal.remove();
    }

    public static void set(OThreadContext threadContext) {
        threadLocal.set(threadContext);
    }
}
