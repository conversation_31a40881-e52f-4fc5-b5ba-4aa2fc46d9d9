package com.taobao.wireless.orange.common.exception;

/**
 * <AUTHOR>
 */

public enum ExceptionEnum {

    PARAM_INVALID("A68105", "入参不合法"),
    NAMESPACE_NOT_EXIST("A10100", "命名空间不存在"),
    PARAMETER_NOT_FOUND("A10102", "参数不存在"),
    PARAMETER_NOT_ONLINE("A10104", "参数未上线"),
    PARAMETER_KEY_DUPLICATE("A10103", "该参数 KEY 已存在"),
    NAMESPACE_NAME_DUPLICATE("A10101", "该命名空间名字已存在"),
    CONDITION_IS_PUBLISHING("A10105", "条件正在发布中"),
    CONDITION_NOT_FOUND("A10106", "条件不存在"),
    RELEASE_ORDER_NOT_EXIST("A10106", "发布单不存在"),
    RELEASE_ORDER_STATUS_NOT_VERIFY_PASS("A10107", "发布单未验证通过"),
    RELEASE_ORDER_STATUS_INVALID("A10108", "发布单状态不正确"),
    CONDITION_PREVIOUS_RELEASE_VERSION_NOT_MATCH("A10106", "条件[name={}]已经有新的版本"),
    CONDITION_NAME_DUPLICATE("A10106", "条件名字[{}]已存在"),
    PARAMETER_PREVIOUS_RELEASE_VERSION_NOT_MATCH("A10107", "参数[key={}]已经有新的版本"),
    PARAMETER_CONDITION_PREVIOUS_RELEASE_VERSION_NOT_MATCH("A10107", "参数[key={}]条件[name={}]已经有新的版本"),
    PARAMETER_IS_PUBLISHING("A10105", "参数正在发布中"),
    CREATE_NAMESPACE_FAIL("B10101", "创建命名空间失败"),
    NAMESPACE_NOT_FOUND("B10100", "命名空间不存在"),

    CREATE_RELEASE_ORDER_FAIL("B10104", "创建发布单失败"),
    CREATE_CONDITION_FAIL("B10103", "创建条件失败"),
    CREATE_PARAMETER_FAIL("B10103", "创建参数失败"),
    OSS_READ_ERROR("B10105", "读取 OSS 数据失败"),
    INDEX_DESERIALIZE_ERROR("B10106", "反序列化索引失败"),
    DESERIALIZE_EXCEPTION("B68105", "反序列化异常"),
    UPDATE_NAMESPACE_FAIL("B10102", "更新命名空间失败"),
    SYSTEM_EXCEPTION("B68101", "Orange 服务系统异常"),
    AMDP_ERROR("B81005", "Amdp服务异常"),
    BEAN_COPY_EXCEPTION("B81005", "BEAN 拷贝异常");


    private String code;
    private String message;

    private ExceptionEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    // 用于生成错误码列表
//    public static void main(String[] args) {
//        System.out.println("| 错误码 | 错误详情 | \n|-----|------|");
//        for (ExceptionEnum exceptionEnum : ExceptionEnum.values()) {
//            System.out.println("| " + exceptionEnum.getCode() + " | " + exceptionEnum.getMessage() + " |");
//        }
//    }
}
