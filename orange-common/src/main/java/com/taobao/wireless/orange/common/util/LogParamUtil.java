package com.taobao.wireless.orange.common.util;

import com.taobao.hsf.context.RPCContext;
import com.taobao.hsf.domain.HSFResponse;
import com.taobao.hsf.invocation.Invocation;
import com.taobao.hsf.invocation.RPCResult;
import com.taobao.hsf.plugins.eagleeye.EagleEyeConstants;
import com.taobao.hsf.util.RequestCtxUtil;
import com.taobao.wireless.orange.common.constant.enums.LogType;
import com.taobao.wireless.orange.common.constant.enums.ProtocolType;
import com.taobao.wireless.orange.common.model.LogParam;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;

/**
 * 日志对象组装工具类
 *
 * <AUTHOR>
 */
public class LogParamUtil {
    private static final Integer MAX_RESULT_SIZE = 4000;

    private LogParamUtil() {
    }

    public static LogParam generateLog(String type, Invocation invocation, String input, Exception e) {
        try {
            LogParam logParam = generateBaseLogParam(type, invocation);
            if (input != null) {
                logParam.setInput(input);
            }

            // 出参
            logParam.setOutput(e.getMessage());
            logParam.setSuccess(false);
            logParam.setCode("500");

            return logParam;
        } catch (Exception exception) {
            return null;
        }
    }

    public static LogParam generateLog(String type, Invocation invocation, String input, RPCResult rpcResult) {
        try {
            LogParam logParam = generateBaseLogParam(type, invocation);

            if (input != null) {
                logParam.setInput(abbreviationStr(input, MAX_RESULT_SIZE));
            }

            // 出参补充
            logParam.setOutput(generateOutput(rpcResult));

            Boolean success = generateSuccess(rpcResult);
            // 暂时为了给 success 为空的占位 -
            logParam.setSuccess(success);

            if (success) {
                // 正常情况下不关注 code, 直接赋值，减少反射开销
                logParam.setCode("200");
            } else {
                logParam.setCode(generateCode(rpcResult));
            }

            return logParam;
        } catch (Exception exception) {
            return null;
        }
    }


    private static LogParam generateBaseLogParam(LogType type, Invocation invocation) {
        LogParam logParam = LogParam.builder().type(type).build();

        logParam.setProtocolType(ProtocolType.HSF);
        logParam.setEnv(System.getenv("envSign"));
        logParam.setSystem(System.getProperty("spring.application.name", "local"));

        logParam.setCalledSystem(RequestCtxUtil.getAppNameOfClient());
        logParam.setRemoteIp(LogType.HSF_SERVER.equals(type) ? invocation.getPeerIP()
                : invocation.getInvokerContext().getRemoteIp());
        logParam.setLocalIp(RequestCtxUtil.getLocalIp());
        logParam.setPath(invocation.getTargetServiceUniqueName());
        logParam.setMethod(invocation.getMethodName());

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:sss");
        Long startTime = invocation.getStartTime();
        logParam.setStartTime(sdf.format(startTime));
        logParam.setEndTime(sdf.format(System.currentTimeMillis()));
        logParam.setCostTime(System.currentTimeMillis() - startTime);

        logParam.setTraceId((String) invocation.get(EagleEyeConstants.EAGLEEYE_TRACE_ID_KEY));
        logParam.setRpcId((String) invocation.get(EagleEyeConstants.EAGLEEYE_RPC_ID_KEY));

        Object[] args = invocation.getMethodArgs();
        logParam.setInput(abbreviationStr(generateInput(args), MAX_RESULT_SIZE));

        return logParam;
    }

    private static String generateInput(Object[] args) {
        return JSON.toJSONString(args);
    }

    private static String generateOutput(RPCResult rpcResult) {
        HSFResponse hsfResponse = rpcResult.getHsfResponse();
        if (hsfResponse.getErrorMsg() != null && !"".equals(hsfResponse.getErrorMsg())) {
            return hsfResponse.getErrorMsg();
        }

        Object result = rpcResult.getAppResponse();
        if (result == null) {
            return null;
        }
        if (result instanceof Throwable) {
            return ((Throwable) result).getMessage();
        }
        String resultStr = JSON.toJSONString(result);
        // 对于大返回值的请求进行结果删减
        return abbreviationStr(resultStr, MAX_RESULT_SIZE);
    }

    private static String abbreviationStr(String content, Integer size) {
        if (content.length() <= size) {
            return content;
        }
        String connectWord = "....";
        Integer halfWordLength = (size - connectWord.length()) / 2;
        return content.substring(0, halfWordLength) + connectWord + content.substring(
                content.length() - halfWordLength);
    }

    private static Boolean generateSuccess(RPCResult rpcResult) {
        HSFResponse hsfResponse = rpcResult.getHsfResponse();
        if (hsfResponse.getErrorType() != null) {
            return false;
        }

        Object result = rpcResult.getAppResponse();
        if (result == null) {
            return null;
        }
        if (result instanceof Throwable) {
            return false;
        }

        if (result instanceof HashMap) {
            if (((HashMap) result).get("success") != null) {
                return "true".equals(((HashMap) result).get("success"));
            }
            if (((HashMap) result).get("isSuccess") != null) {
                return "true".equals(((HashMap) result).get("isSuccess"));
            }
        } else if (result instanceof JSONObject) {
            Object success = ((JSONObject) result).get("success");
            if (success != null) {
                return "true".equals(success);
            }
        } else if (result instanceof ArrayList) {
            // 默认返回数组表示请求成功，兼容一些不符合规范的请求返回
            return true;
        } else {
            String val1 = getValueFromObject(result, "isSuccess");
            if (val1 != null) {
                return "true".equals(val1);
            }
            return "true".equals(getValueFromObject(result, "getSuccess"));
        }
        return null;
    }

    private static String generateCode(RPCResult rpcResult) {
        HSFResponse hsfResponse = rpcResult.getHsfResponse();
        if (hsfResponse.isError()) {
            return "500.hsf.error";
        }
        if (hsfResponse.isTimeout()) {
            return "500.hsf.timeout";
        }

        Object result = rpcResult.getAppResponse();
        if (result == null) {
            return null;
        }
        if (result instanceof Throwable) {
            return "500.hsf.fail";
        }

        if (result instanceof HashMap) {
            if (((HashMap) result).get("code") != null) {
                return ((HashMap) result).get("code").toString();
            }
            if (((HashMap) result).get("errorCode") != null) {
                return ((HashMap) result).get("errorCode").toString();
            }
        } else if (result instanceof JSONObject) {
            Object code = ((JSONObject) result).get("returnCode");
            if (code != null) {
                return code.toString();
            }
        } else {
            String val1 = getValueFromObject(result, "getCode");
            if (val1 != null) {
                return val1;
            }
            return getValueFromObject(result, "getErrorCode");
        }
        return null;
    }

    private static String getValueFromObject(Object obj, String method) {
        try {
            return obj.getClass().getMethod(method).invoke(obj).toString();
        } catch (Exception e) {
            return null;
        }
    }
}
