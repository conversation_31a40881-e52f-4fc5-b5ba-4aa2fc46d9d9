package com.taobao.wireless.orange.common.filter;

import com.taobao.eagleeye.EagleEye;
import com.alibaba.fastjson.JSON;
import com.taobao.hsf.invocation.Invocation;
import com.taobao.hsf.invocation.InvocationHandler;
import com.taobao.hsf.invocation.RPCResult;
import com.taobao.hsf.invocation.filter.ClientFilter;
import com.taobao.hsf.plugins.eagleeye.EagleEyeConstants;
import com.taobao.hsf.util.concurrent.ListenableFuture;
import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.model.LogParam;
import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * HSF Client 统一日志切面
 * 记录HSF客户端调用的详细信息，包括请求参数、响应结果、执行时间等
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@Slf4j(topic = "hsf-client")
public class LogHSFClientFilter implements ClientFilter {

    private static final String LOG_PARAM_KEY = "LOG_PARAM";
    private static final String START_TIME_KEY = "START_TIME";

    public LogHSFClientFilter() {
    }

    @Override
    public ListenableFuture<RPCResult> invoke(InvocationHandler nextHandler, Invocation invocation) throws Throwable {
        String eTraceId = EagleEye.getTraceId();
        if (eTraceId != null) {
            invocation.put(EagleEyeConstants.EAGLEEYE_TRACE_ID_KEY, EagleEye.getTraceId());
        }

        invocation.put(EagleEyeConstants.EAGLEEYE_RPC_ID_KEY,
                EagleEye.getRpcId() == null ? "null" : EagleEye.getRpcId());

        try {
            return nextHandler.invoke(invocation);
        } catch (Exception e) {
            handleException(logParam, e, startTime);
            throw e;
        }
    }

    @Override
    public void onResponse(Invocation invocation, RPCResult rpcResult) {
        // 从invocation中获取日志参数和开始时间
        LogParam logParam = (LogParam) invocation.get(LOG_PARAM_KEY);
        Long startTime = (Long) invocation.get(START_TIME_KEY);

        if (logParam != null && startTime != null) {
            // 组装日志对象并打印
            assembleAndLogResult(logParam, rpcResult, startTime);
        }
    }

    /**
     * 组装日志对象并打印
     */
    private void assembleAndLogResult(LogParam logParam, RPCResult rpcResult, long startTime) {
        long endTime = System.currentTimeMillis();
        logParam.setCostTime(endTime - startTime);
        logParam.setEndTime(formatDate(new Date(endTime)));

        if (rpcResult != null && rpcResult.isSuccess()) {
            logParam.setSuccess(true);
            logParam.setOutput(JSON.toJSONString(rpcResult.getAppResponse()));
        } else {
            logParam.setSuccess(false);
            if (rpcResult != null && rpcResult.getException() != null) {
                logParam.setOutput(rpcResult.getException().getMessage());
            }
        }

        // 打印日志
        if (logParam.isSuccess()) {
            log.info(JSON.toJSONString(logParam));
        } else {
            log.error(JSON.toJSONString(logParam));
        }
    }

    /**
     * 处理HSF调用异常
     */
    private void handleException(LogParam logParam, Exception e, long startTime) {
        long endTime = System.currentTimeMillis();
        logParam.setCostTime(endTime - startTime);
        logParam.setEndTime(formatDate(new Date(endTime)));
        logParam.setSuccess(false);
        logParam.setOutput(e.getMessage());

        log.error(JSON.toJSONString(logParam));
    }

    /**
     * 获取HSF请求参数
     */
    private Map<String, Object> getRequestParams(Invocation invocation) {
        Map<String, Object> requestParams = new HashMap<>();

        Object[] args = invocation.getArguments();
        Class<?>[] parameterTypes = invocation.getParameterTypes();

        if (args != null && parameterTypes != null) {
            for (int i = 0; i < args.length && i < parameterTypes.length; i++) {
                String paramName = "arg" + i + "(" + parameterTypes[i].getSimpleName() + ")";
                requestParams.put(paramName, args[i]);
            }
        }

        return requestParams;
    }

    /**
     * 获取本地IP地址
     */
    private String getLocalIp() {
        try {
            return InetAddress.getLocalHost().getHostAddress();
        } catch (Exception e) {
            return "unknown";
        }
    }

    /**
     * 格式化日期为字符串
     */
    private String formatDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        return sdf.format(date);
    }
}
