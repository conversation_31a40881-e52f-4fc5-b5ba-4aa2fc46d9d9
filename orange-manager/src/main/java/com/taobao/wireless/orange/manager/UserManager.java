package com.taobao.wireless.orange.manager;

import com.taobao.wireless.orange.common.model.User;
import com.taobao.wireless.orange.external.user.AmdpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class UserManager {

    @Autowired
    private AmdpService amdpService;

    /**
     * 根据工号批量查询用户
     */
    public Map<String, User> queryUserByWorkNoList(List<String> workNoList) {
        // todo: 需要支持批量
        return amdpService.queryUserByWorkNoList(workNoList);
    }
}
