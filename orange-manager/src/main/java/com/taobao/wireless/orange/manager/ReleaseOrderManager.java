package com.taobao.wireless.orange.manager;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.annotation.OperationLog;
import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.SerializeUtil;
import com.taobao.wireless.orange.dal.enhanced.dao.*;
import com.taobao.wireless.orange.dal.enhanced.entity.*;
import com.taobao.wireless.orange.dal.enhanced.entity.ONamespaceDO;
import com.taobao.wireless.orange.manager.model.*;
import com.taobao.wireless.orange.manager.util.PageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.taobao.wireless.orange.common.constant.Common.DEFAULT_CONDITION_ID;

@Service
public class ReleaseOrderManager {
    @Autowired
    private ParameterManager parameterManager;
    @Autowired
    private NamespaceManager namespaceManager;

    @Autowired
    private ConditionManager conditionManager;

    @Autowired
    private OReleaseOrderDAO releaseOrderDAO;

    @Autowired
    private NamespaceVersionManager namespaceVersionManager;

    @Autowired
    private OParameterConditionVersionDAO parameterConditionVersionDAO;

    @Autowired
    private OParameterVersionDAO parameterVersionDAO;

    @Autowired
    private OConditionVersionDAO conditionVersionDAO;

    @Autowired
    private ONamespaceVersionContentDAO namespaceVersionContentDAO;

    @Autowired
    private ConfigManager configManager;

    @Autowired
    private FullReleaseConfigGenerator fullReleaseConfigGenerator;
    @Autowired
    private ONamespaceDAO namespaceDAO;

    @Autowired
    private OReleaseOrderOperationDAO releaseOrderOperationDAO;


    public Page<OReleaseOrderDO> query(OReleaseOrderDO query, Integer page, Integer size) {
        return releaseOrderDAO.lambdaQuery()
                .eq(query.getAppKey() != null, OReleaseOrderDO::getAppKey, query.getAppKey())
                .eq(query.getNamespaceId() != null, OReleaseOrderDO::getNamespaceId, query.getNamespaceId())
                .eq(query.getReleaseVersion() != null, OReleaseOrderDO::getReleaseVersion, query.getReleaseVersion())
                .eq(query.getStatus() != null, OReleaseOrderDO::getStatus, query.getStatus())
                .orderByDesc(OReleaseOrderDO::getId)
                .page(PageUtil.build(page, size));
    }

    /**
     * 新建发布
     *
     * @param releaseOrderBO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(type = OperationType.CREATE)
    public String create(ReleaseOrderBO releaseOrderBO) {
        ONamespaceDO namespace = namespaceManager.getByNamespaceId(releaseOrderBO.getNamespaceId());
        releaseOrderBO.setAppKey(namespace.getAppKey());
        // todo: 获取 namespace 锁

        // 检测涉及发布实体是否有在变更中
        checkReleaseObjectIsPublishing(releaseOrderBO);

        // 生成发布单
        String releaseVersion = createReleaseOrder(releaseOrderBO);

        // 新增条件版本记录
        conditionManager.createConditionVersions(releaseOrderBO.getAppKey(), releaseOrderBO.getNamespaceId(), releaseVersion, releaseOrderBO.getConditionVersionBOS());

        // 新增参数版本记录
        parameterManager.createParameterVersions(releaseOrderBO.getAppKey(), releaseOrderBO.getNamespaceId(), releaseVersion, releaseOrderBO.getParameterVersionBOS());

        // 新增参数条件版本记录
        parameterManager.createParameterConditionVersions(releaseOrderBO.getAppKey(), releaseOrderBO.getNamespaceId(), releaseVersion, releaseOrderBO.getParameterVersionBOS(), releaseOrderBO.getConditionVersionBOS());

        // 新增 namespace 版本记录
        namespaceVersionManager.create(releaseOrderBO.getNamespaceId(), releaseOrderBO.getAppKey(), releaseVersion);

        // todo: 释放 namespace 锁
        return releaseVersion;
    }

    /**
     * 正式发布
     *
     * @param releaseVersion
     */
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(type = OperationType.RELEASE)
    public void publish(String releaseVersion) {
        OReleaseOrderDO releaseOrder = releaseOrderDAO.lambdaQuery()
                .eq(OReleaseOrderDO::getReleaseVersion, releaseVersion)
                .oneOpt()
                .orElseThrow(() -> new CommonException(ExceptionEnum.RELEASE_ORDER_NOT_EXIST));
        // fixme: 为了测试去掉先
//        if (!ReleaseOrderStatus.VERIFY_PASS.equals(releaseOrder.getStatus())) {
//            throw new CommonException(ExceptionEnum.RELEASE_ORDER_STATUS_NOT_VERIFY_PASS);
//        }

        // todo: 获取 namespace 锁
        List<OParameterVersionDO> parameterVersionDOS = parameterVersionDAO.lambdaQuery().eq(OParameterVersionDO::getReleaseVersion, releaseVersion).list();
        onlineParameterVersion(parameterVersionDOS);

        List<OConditionVersionDO> conditionVersionDOS = conditionVersionDAO.lambdaQuery().eq(OConditionVersionDO::getReleaseVersion, releaseVersion).list();
        onlineConditionVersion(conditionVersionDOS);

        List<OParameterConditionVersionDO> parameterConditionVersionDOS = parameterConditionVersionDAO.lambdaQuery().eq(OParameterConditionVersionDO::getReleaseVersion, releaseVersion).list();
        onlineParameterConditionVersion(parameterConditionVersionDOS);

        String namespaceVersion = namespaceVersionManager.upgrade(releaseOrder.getNamespaceId(), releaseVersion, NamespaceVersionChangeType.FINISH_RELEASE);
        ONamespaceVersionContentDO namespaceVersionContentDO = new ONamespaceVersionContentDO();
        namespaceVersionContentDO.setNamespaceId(releaseOrder.getNamespaceId());
        namespaceVersionContentDO.setNamespaceVersion(namespaceVersion);
        namespaceVersionContentDO.setAppKey(releaseOrder.getAppKey());

        NamespaceIdNamePairBO namespace = Optional.ofNullable(namespaceDAO.getByNamespaceId(releaseOrder.getNamespaceId()))
                .map(n -> NamespaceIdNamePairBO.builder().name(n.getName()).namespaceId(n.getNamespaceId()).build())
                .orElseThrow(() -> new CommonException(ExceptionEnum.NAMESPACE_NOT_EXIST));
        Optional.ofNullable(fullReleaseConfigGenerator.generate(namespace, "0"))
                .ifPresent(content -> {
                    namespaceVersionContentDO.setContent(JSON.toJSONString(content));
                });
        namespaceVersionContentDAO.save(namespaceVersionContentDO);

        OReleaseOrderDO updateReleaseOrder = new OReleaseOrderDO();
        updateReleaseOrder.setId(releaseOrder.getId());
        updateReleaseOrder.setStatus(ReleaseOrderStatus.RELEASED);
        releaseOrderDAO.updateById(updateReleaseOrder);
        // todo: 释放 namespace 锁
    }

    @Transactional(rollbackFor = Exception.class)
    @OperationLog(type = OperationType.CANCEL)
    public void cancel(String releaseVersion) {
        var releaseOrder = releaseOrderDAO.lambdaQuery()
                .eq(OReleaseOrderDO::getReleaseVersion, releaseVersion)
                .oneOpt()
                .orElseThrow(() -> new CommonException(ExceptionEnum.RELEASE_ORDER_NOT_EXIST));

        if (releaseOrder.getStatus().equals(ReleaseOrderStatus.RELEASED) || releaseOrder.getStatus().equals(ReleaseOrderStatus.CANCELED)) {
            throw new CommonException(ExceptionEnum.RELEASE_ORDER_STATUS_INVALID);
        }

        // todo: 获取 namespace 锁
        releaseOrderDAO.lambdaUpdate()
                .eq(OReleaseOrderDO::getReleaseVersion, releaseVersion)
                .set(OReleaseOrderDO::getStatus, ReleaseOrderStatus.CANCELED)
                .update();

        parameterVersionDAO.lambdaUpdate()
                .eq(OParameterVersionDO::getReleaseVersion, releaseVersion)
                .set(OParameterVersionDO::getStatus, VersionStatus.CANCELED).update();

        conditionVersionDAO.lambdaUpdate()
                .eq(OConditionVersionDO::getReleaseVersion, releaseVersion)
                .set(OConditionVersionDO::getStatus, VersionStatus.CANCELED).update();

        parameterConditionVersionDAO.lambdaUpdate()
                .eq(OParameterConditionVersionDO::getReleaseVersion, releaseVersion)
                .set(OParameterConditionVersionDO::getStatus, VersionStatus.CANCELED).update();

        namespaceVersionManager.upgrade(releaseOrder.getNamespaceId(), releaseVersion, NamespaceVersionChangeType.CANCEL_RELEASE);

        // todo: 释放 namespace 锁
    }

    @Transactional(rollbackFor = Exception.class)
    @OperationLog(type = OperationType.VERIFY)
    public void verify(String releaseVersion) {
        // todo: 获取 namespace 锁
        releaseOrderDAO.lambdaUpdate()
                .eq(OReleaseOrderDO::getReleaseVersion, releaseVersion)
                .set(OReleaseOrderDO::getStatus, ReleaseOrderStatus.VERIFY_PASS)
                .update();

        // todo: 释放 namespace 锁
    }

    @Transactional(rollbackFor = Exception.class)
    @OperationLog(type = OperationType.RATIO_GRAY)
    public void ratioGray(String releaseVersion, int percent) {
        // todo: 获取 namespace 锁
        var releaseOrder = getReleaseOrderByReleaseVersion(releaseVersion);
        // todo: 状态检查
        if (releaseOrder.getPercent() != null && releaseOrder.getPercent() > percent) {
            throw new CommonException(ExceptionEnum.PARAM_INVALID, "灰度比例不能小于已有的灰度比例");
        }

        releaseOrderDAO.lambdaUpdate()
                .eq(OReleaseOrderDO::getReleaseVersion, releaseVersion)
                .set(OReleaseOrderDO::getPercent, percent)
                .set(OReleaseOrderDO::getStatus, ReleaseOrderStatus.IN_RATIO_GRAY)
                .update();
        // todo: 释放 namespace 锁
    }

    public List<ParameterChangeBO> getChanges(String releaseVersion) {
        // 查询本次发布单涉及的参数
        List<OParameterVersionDO> parameters = parameterVersionDAO.lambdaQuery().eq(OParameterVersionDO::getReleaseVersion, releaseVersion).list();
        List<String> parameterIds = parameters.stream().map(OParameterVersionDO::getParameterId).collect(Collectors.toList());
        Map<String, List<OParameterConditionVersionDO>> initParameterConditionVersions = parameterManager.getParameterConditionVersionsByStatus(parameterIds, VersionStatus.INIT);

        // 查询参数线上值
        Map<String, OParameterVersionDO> releaseParameterVersions = parameterManager.getParameterVersionsByStatus(parameterIds, VersionStatus.RELEASED);
        Map<String, List<OParameterConditionVersionDO>> releasedParameterConditionVersions = parameterManager.getParameterConditionVersionsByStatus(parameterIds, VersionStatus.RELEASED);

        return parameters.stream().map(version -> {
            ParameterChangeBO parameterChange = new ParameterChangeBO();

            // 本次修改的内容
            ParameterVersionBO change = BeanUtil.createFromProperties(version, ParameterVersionBO.class);
            Optional.ofNullable(initParameterConditionVersions.get(version.getParameterId()))
                    .ifPresent(i -> {
                        change.setParameterConditionVersionBOS(BeanUtil.createFromProperties(i, ParameterConditionVersionBO.class));
                    });
            parameterChange.setChange(change);

            // 修改前的参数信息（即：线上参数信息
            OParameterVersionDO oldParameterVersion = releaseParameterVersions.get(version.getParameterId());
            if (oldParameterVersion != null) {
                ParameterVersionBO before = BeanUtil.createFromProperties(oldParameterVersion, ParameterVersionBO.class);
                Optional.ofNullable(releasedParameterConditionVersions.get(version.getParameterId()))
                        .ifPresent(i -> {
                            var conditionId2ParameterCondition = BeanUtil.createFromProperties(i, ParameterConditionVersionBO.class).stream().collect(Collectors.toMap(ParameterConditionVersionBO::getConditionId, Function.identity()));

                            List<ParameterConditionVersionBO> sortedParameterConditions = parameterManager.getConditionsOrder(before).stream().map(conditionId2ParameterCondition::get).collect(Collectors.toList());
                            sortedParameterConditions.add(conditionId2ParameterCondition.get(DEFAULT_CONDITION_ID));

                            before.setParameterConditionVersionBOS(sortedParameterConditions);
                        });
                parameterChange.setBefore(before);
            }

            return parameterChange;
        }).collect(Collectors.toList());

        // todo: 参数变更
//        List<OConditionVersionDO> conditions = conditionVersionDAO.lambdaQuery().eq(OConditionVersionDO::getReleaseVersion, releaseVersion).list();
    }

    public List<OReleaseOrderOperationDO> getOperations(String releaseVersion) {
        return releaseOrderOperationDAO.lambdaQuery()
                .eq(OReleaseOrderOperationDO::getReleaseVersion, releaseVersion)
                .orderByDesc(OReleaseOrderOperationDO::getId)
                .list();
    }

    public ReleaseOrderBO getDetail(String releaseVersion) {
        OReleaseOrderDO releaseOrderDO = releaseOrderDAO.getByReleaseVersion(releaseVersion);
        ReleaseOrderBO releaseOrder = BeanUtil.createFromProperties(releaseOrderDO, ReleaseOrderBO.class);
        // 填充涉及变动的参数和条件
        List<OParameterVersionDO> parameters = parameterVersionDAO.lambdaQuery().eq(OParameterVersionDO::getReleaseVersion, releaseVersion).list();
        List<OConditionVersionDO> conditions = conditionVersionDAO.lambdaQuery().eq(OConditionVersionDO::getReleaseVersion, releaseVersion).list();

        releaseOrder.setParameterVersionBOS(BeanUtil.createFromProperties(parameters, ParameterVersionBO.class));
        releaseOrder.setConditionVersionBOS(BeanUtil.createFromProperties(conditions, ConditionVersionBO.class));
        return releaseOrder;
    }


    private void onlineParameterVersion(List<OParameterVersionDO> parameterVersions) {
        if (CollectionUtils.isEmpty(parameterVersions)) {
            return;
        }

        List<String> parameterIds = parameterVersions.stream().map(OParameterVersionDO::getParameterId).collect(Collectors.toList());
        parameterVersionDAO.lambdaUpdate()
                .in(OParameterVersionDO::getParameterId, parameterIds)
                .eq(OParameterVersionDO::getStatus, VersionStatus.RELEASED)
                .set(OParameterVersionDO::getStatus, VersionStatus.OUTDATED)
                .update();

        List<OParameterVersionDO> releaseOrderIds = parameterVersions.stream()
                .filter(i -> !ParameterChangeType.DELETE.equals(i.getChangeType()))
                .map(i -> {
                    OParameterVersionDO updateParameterVersionDO = new OParameterVersionDO();
                    updateParameterVersionDO.setId(i.getId());
                    updateParameterVersionDO.setStatus(VersionStatus.RELEASED);
                    return updateParameterVersionDO;
                }).collect(Collectors.toList());
        parameterVersionDAO.updateBatchById(releaseOrderIds);
    }

    private void onlineConditionVersion(List<OConditionVersionDO> conditionVersions) {
        if (CollectionUtils.isEmpty(conditionVersions)) {
            return;
        }

        List<String> conditionIds = conditionVersions.stream().map(OConditionVersionDO::getConditionId).collect(Collectors.toList());
        conditionVersionDAO.lambdaUpdate()
                .in(OConditionVersionDO::getConditionId, conditionIds)
                .eq(OConditionVersionDO::getStatus, VersionStatus.RELEASED)
                .set(OConditionVersionDO::getStatus, VersionStatus.OUTDATED)
                .update();

        List<OConditionVersionDO> releaseOrderIds = conditionVersions.stream()
                .filter(i -> !ConditionChangeType.DELETE.equals(i.getChangeType()))
                .map(i -> {
                    OConditionVersionDO updateConditionVersionDO = new OConditionVersionDO();
                    updateConditionVersionDO.setId(i.getId());
                    updateConditionVersionDO.setStatus(VersionStatus.RELEASED);
                    return updateConditionVersionDO;
                }).collect(Collectors.toList());
        conditionVersionDAO.updateBatchById(releaseOrderIds);
    }

    private void onlineParameterConditionVersion(List<OParameterConditionVersionDO> parameterConditionVersions) {
        if (CollectionUtils.isEmpty(parameterConditionVersions)) {
            return;
        }
        LambdaUpdateChainWrapper<OParameterConditionVersionDO> updateChainWrapper = parameterConditionVersionDAO.lambdaUpdate();
        for (OParameterConditionVersionDO parameterConditionVersion : parameterConditionVersions) {
            updateChainWrapper.or(i -> i.eq(OParameterConditionVersionDO::getParameterId, parameterConditionVersion.getParameterId())
                    .eq(OParameterConditionVersionDO::getConditionId, parameterConditionVersion.getConditionId())
                    .eq(OParameterConditionVersionDO::getStatus, VersionStatus.RELEASED));
        }

        updateChainWrapper.set(OParameterConditionVersionDO::getStatus, VersionStatus.OUTDATED).update();
        List<OParameterConditionVersionDO> releaseOrderIds = parameterConditionVersions.stream()
                .filter(i -> !ParameterConditionChangeType.DELETE.equals(i.getChangeType()))
                .map(i -> {
                    OParameterConditionVersionDO updateParameterConditionVersionDO = new OParameterConditionVersionDO();
                    updateParameterConditionVersionDO.setId(i.getId());
                    updateParameterConditionVersionDO.setStatus(VersionStatus.RELEASED);
                    return updateParameterConditionVersionDO;
                }).collect(Collectors.toList());
        parameterConditionVersionDAO.updateBatchById(releaseOrderIds);
    }

    private String createReleaseOrder(ReleaseOrderBO releaseOrderBO) {
        String releaseVersion = String.valueOf(SerializeUtil.version());
        releaseOrderBO.setReleaseVersion(releaseVersion);
        releaseOrderBO.setBizType(ReleaseOrderBizType.NAMESPACE);
        releaseOrderBO.setBizId(releaseOrderBO.getNamespaceId());
        releaseOrderBO.setStatus(ReleaseOrderStatus.INIT);
        releaseOrderBO.setReleaseType(ReleaseType.PUBLISH);

        // 新增发布单
        boolean success = releaseOrderDAO.save(releaseOrderBO);
        if (!success) {
            throw new CommonException(ExceptionEnum.CREATE_RELEASE_ORDER_FAIL);
        }
        return releaseVersion;
    }

    /**
     * 检测发布实体是否在发布中
     *
     * @param releaseOrderBO
     */
    private void checkReleaseObjectIsPublishing(ReleaseOrderBO releaseOrderBO) {
        if (CollectionUtils.isNotEmpty(releaseOrderBO.getParameterVersionBOS())) {
            List<String> parameterIds = releaseOrderBO.getParameterVersionBOS().stream().map(ParameterVersionBO::getParameterId).collect(Collectors.toList());

            Long count = parameterVersionDAO.lambdaQuery().in(OParameterVersionDO::getParameterId, parameterIds).eq(OParameterVersionDO::getStatus, VersionStatus.INIT).count();
            if (count > 0) {
                throw new CommonException(ExceptionEnum.PARAMETER_IS_PUBLISHING);
            }
        }

        if (CollectionUtils.isNotEmpty(releaseOrderBO.getConditionVersionBOS())) {
            List<String> conditionIds = releaseOrderBO.getConditionVersionBOS().stream().map(ConditionVersionBO::getConditionId).collect(Collectors.toList());

            Long count = conditionVersionDAO.lambdaQuery().in(OConditionVersionDO::getConditionId, conditionIds).eq(OConditionVersionDO::getStatus, VersionStatus.INIT).count();
            if (count > 0) {
                throw new CommonException(ExceptionEnum.CONDITION_IS_PUBLISHING);
            }
        }
    }

    private OReleaseOrderDO getReleaseOrderByReleaseVersion(String releaseVersion) {
        return releaseOrderDAO.lambdaQuery()
                .eq(OReleaseOrderDO::getReleaseVersion, releaseVersion)
                .oneOpt()
                .orElseThrow(() -> new CommonException(ExceptionEnum.RELEASE_ORDER_NOT_EXIST));
    }
}
