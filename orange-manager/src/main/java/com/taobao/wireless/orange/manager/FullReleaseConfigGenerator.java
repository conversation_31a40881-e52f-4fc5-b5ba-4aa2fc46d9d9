package com.taobao.wireless.orange.manager;

import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.constant.enums.ConfigStrategy;
import com.taobao.wireless.orange.common.constant.enums.ConfigType;
import com.taobao.wireless.orange.common.constant.enums.VersionStatus;
import com.taobao.wireless.orange.dal.enhanced.entity.OConditionVersionDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OParameterVersionDO;
import com.taobao.wireless.orange.manager.model.*;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 全量发布配置生成器
 * 负责生成包含所有已发布参数的完整配置
 */
@Component
public class FullReleaseConfigGenerator extends ConfigGenerator<ReleaseConfig> {

    /**
     * 获取发布版本列表
     * 全量发布模式不需要版本过滤，返回空字符串列表
     *
     * @return 发布版本列表
     */
    @Override
    protected List<String> getReleaseVersions(String namespaceId, String baseVersion) {
        // 全量模式不需要版本过滤，返回空列表
        return Collections.emptyList();
    }

    /**
     * 获取参数版本列表
     * 获取所有已发布状态的参数
     *
     * @param notUsed 发布版本列表
     * @return 参数版本列表
     */
    @Override
    protected List<OParameterVersionDO> getParameterVersions(String namespaceId, List<String> notUsed) {
        return parameterVersionDAO.lambdaQuery()
                .eq(OParameterVersionDO::getNamespaceId, namespaceId)
                .eq(OParameterVersionDO::getStatus, VersionStatus.RELEASED)
                .list();
    }

    /**
     * 构建结果配置
     * 生成包含所有条件和参数的完整配置
     *
     * @param namespace         命名空间信息
     * @param parameters        参数列表
     * @param offlineParameters 下线参数列表
     * @param conditionMap      条件映射
     * @return 完整的发布配置
     */
    @Override
    protected ReleaseConfig buildResult(NamespaceIdNamePairBO namespace, List<Parameter> parameters, List<String> offlineParameters, Map<String, OConditionVersionDO> conditionMap) {
        List<Condition> conditions = conditionMap.values().stream()
                .map(c -> Condition.builder()
                        .id(c.getConditionId())
                        .expression(JSON.parse(c.getExpression(), Expression.class))
                        .build())
                // 端上依赖排序，加速检索
                .sorted(Comparator.comparing(Condition::getId))
                .collect(Collectors.toList());

        return ReleaseConfig.builder()
                .schemaVersion("1.0")
                .namespace(namespace.getName())
                .strategy(ConfigStrategy.FULL)
                .type(ConfigType.RELEASE)
                .conditions(conditions)
                .parameters(parameters)
                .offlineParameters(offlineParameters)  // 使用传入的offlineParameters参数
                .build();
    }

    /**
     * 获取版本状态列表
     * 全量发布只关注已发布状态的配置
     *
     * @return 版本状态列表
     */
    @Override
    protected List<VersionStatus> getVersionStatuses() {
        return List.of(VersionStatus.RELEASED);
    }
}
