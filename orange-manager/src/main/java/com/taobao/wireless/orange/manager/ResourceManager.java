package com.taobao.wireless.orange.manager;

import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.constant.enums.ResourceType;
import com.taobao.wireless.orange.common.util.SerializeUtil;
import com.taobao.wireless.orange.dal.enhanced.dao.OResourceDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.OResourceDO;
import com.taobao.wireless.orange.external.OssService;
import com.taobao.wireless.orange.manager.model.ResourceSerializable;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;

@Service
public class ResourceManager {
    @Autowired
    private OssService ossService;
    @Autowired
    private OResourceDAO resourceDAO;

    @Value("${orange.oss.bucketName}")
    private String bucketName;

    /**
     * Create a resource from a serializable object
     *
     * @param content The object to serialize
     * @param type    The resource type
     * @return The created resource
     */
    public OResourceDO create(ResourceSerializable content, ResourceType type) {
        // fixme
        String resourceId = SerializeUtil.UUID() + ".bin";
        byte[] data = content.serialize();
        ossService.uploadData(data, bucketName, resourceId);

        OResourceDO resource = new OResourceDO();
        resource.setResourceId(resourceId);
        resource.setSrcContent(JSON.toJSONString(content));
        resource.setData(Arrays.toString(data));
        resource.setMd5(DigestUtils.md5Hex(data));
        resource.setType(type);

        resourceDAO.save(resource);
        return resource;
    }
}
