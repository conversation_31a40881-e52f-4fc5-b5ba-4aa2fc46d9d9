package com.taobao.wireless.orange.manager.util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.util.BeanUtil;

import java.util.Optional;

public class PageUtil {
    private final static int DEFAULT_PAGE_SIZE = 10;

    public static <S, T> PaginationResult<T> convert(Page<S> result, Class<T> targetType) {
        PaginationResult<T> paginationResult = BeanUtil.createFromProperties(result, PaginationResult.class);
        paginationResult.setData(BeanUtil.createFromProperties(result.getRecords(), targetType));
        return paginationResult;
    }

    public static <T> Page<T> build(Integer page, Integer size) {
        int currentPage = Optional.ofNullable(page).orElse(1);
        int pageSize = Optional.ofNullable(size).orElse(DEFAULT_PAGE_SIZE);
        return new Page<T>(currentPage, pageSize);
    }
}
